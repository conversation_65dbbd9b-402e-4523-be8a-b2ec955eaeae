package com.sgmw.common.config

/**
 * 内存优化配置类
 * 集中管理内存相关的配置参数
 */
object MemoryOptimizationConfig {
    
    // Glide配置
    object GlideConfig {
        const val MEMORY_CACHE_SIZE_MB = 50 // 内存缓存大小50MB
        const val DISK_CACHE_SIZE_MB = 200 // 磁盘缓存大小200MB
        const val DEFAULT_IMAGE_SIZE = 512 // 默认图片尺寸
        const val THUMBNAIL_SIZE = 200 // 缩略图尺寸
        const val OOM_RECOVERY_SIZE = 200 // OOM恢复时的图片尺寸
    }
    
    // 缓存配置
    object CacheConfig {
        const val KTV_CACHE_SIZE_GB = 1L // KTV缓存大小1GB
        const val MAX_SEARCH_HISTORY_COUNT = 50 // 最大搜索历史数量
        const val MAX_PLAY_HISTORY_COUNT = 100 // 最大播放历史数量
    }
    
    // 内存监控配置
    object MonitorConfig {
        const val MEMORY_WARNING_THRESHOLD = 0.70f // 内存警告阈值70%（降低阈值）
        const val MEMORY_CRITICAL_THRESHOLD = 0.80f // 内存危险阈值80%（降低阈值）
        const val MEMORY_STRICT_THRESHOLD = 0.85f // 内存严格清理阈值85%
        const val MEMORY_EMERGENCY_THRESHOLD = 0.90f // 内存紧急阈值90%
        const val MONITOR_INTERVAL_MS = 20000L // 监控间隔20秒（缩短间隔）
        const val ENABLE_MEMORY_MONITORING = true // 是否启用内存监控
        const val MIN_FREE_MEMORY_MB = 10 // 最小剩余内存10MB
    }
    
    // 数据存储配置
    object StorageConfig {
        const val MAX_MMKV_SIZE_MB = 10 // MMKV最大大小10MB
        const val ENABLE_MMKV_COMPRESSION = true // 启用MMKV压缩
        const val AUTO_SYNC_INTERVAL_MS = 60000L // 自动同步间隔1分钟
    }
    
    // 第三方SDK配置
    object SdkConfig {
        const val SENSORS_DATA_CACHE_SIZE_MB = 5 // 神策数据缓存大小5MB
        const val ENABLE_SENSORS_DATA_COMPRESSION = true // 启用神策数据压缩
        const val MAX_PENDING_EVENTS = 100 // 最大待发送事件数量
    }
}
